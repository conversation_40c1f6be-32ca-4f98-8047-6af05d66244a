
/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-dark: #1a1a2e;
    --secondary-dark: #16213e;
    --accent-gold: #ffd700;
    --accent-gold-dark: #e6c200;
    --accent-gold-light: #fff3a0;
    --text-light: #ffffff;
    --text-gray: #b8b8b8;
    --text-dark: #333333;
    --success-green: #00ff88;
    --danger-red: #ff4757;
    --card-bg: #2a2a3e;
    --border-color: #3a3a4e;

    /* Typography */
    --font-primary: 'Montserrat', sans-serif;
    --font-display: 'Playfair Display', serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.25);
    --shadow-gold: 0 4px 20px rgba(255, 215, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    color: var(--text-light);
    line-height: 1.4;
    min-height: 80vh;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    color: var(--accent-gold);
    position: relative;
    margin-bottom: var(--spacing-md);
}

h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-gold) 0%, transparent 100%);
    border-radius: var(--radius-sm);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: var(--text-light);
}

p {
    font-size: 1.1rem;
    color: var(--text-gray);
    margin-bottom: var(--spacing-sm);
}

/* ===== LAYOUT CONTAINERS ===== */
.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* ===== HEADER & HERO SECTION ===== */
header {
    position: relative;
}

/* Removed complex background for minimalist design */

.hero {
    position: relative;
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-md);
    min-height: 40vh; /* Reduced for minimalist approach */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1;
    background: transparent; /* Clean, minimal background */
    gap: var(--spacing-lg); /* Clean spacing between elements */
}

.hero p {
    font-size: clamp(1.2rem, 2.5vw, 1.5rem);
    color: var(--text-gray);
    margin: var(--spacing-md) 0 var(--spacing-xl);
    max-width: 600px;
}

/* ===== BUTTONS & CTA ===== */
.cta-btn {
    display: inline-block;
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
    color: var(--text-dark);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-gold);
    position: relative;
    overflow: hidden;
}

/* Removed complex button effects for minimalism */

.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, var(--accent-gold-light) 0%, var(--accent-gold) 100%);
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn:active {
    transform: translateY(0);
}

/* ===== MAIN CONTENT SECTIONS ===== */
main {
    position: relative;
    z-index: 1;
}

section {
    margin: var(--spacing-xxl) 0;
    padding: var(--spacing-xl) var(--spacing-md);
    background: rgba(42, 42, 62, 0.3);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: transform var(--transition-normal);
}

section:hover {
    transform: translateY(-5px);
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-us ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.why-choose-us li {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(42, 42, 62, 0.8) 100%);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.why-choose-us li::before {
    content: '✓';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    background: var(--accent-gold);
    color: var(--text-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
}

.why-choose-us li:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-gold);
}

.why-choose-us li:hover::before {
    background: var(--accent-gold-light);
    transform: scale(1.1);
}

/* ===== GAME CATEGORIES SECTION ===== */
.game-categories {
    background: linear-gradient(135deg, rgba(42, 42, 62, 0.5) 0%, rgba(26, 26, 46, 0.5) 100%);
}

.game-categories .category {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(58, 58, 78, 0.8) 100%);
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 2px solid transparent;
    background-clip: padding-box;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.game-categories .category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.game-categories .category:hover {
    transform: translateY(-5px) scale(1.02);
    border-color: var(--accent-gold);
    box-shadow: var(--shadow-gold);
}

.game-categories .category:hover::before {
    opacity: 0.1;
}

.game-categories .category h3 {
    margin: 0;
    text-align: center;
    transition: color var(--transition-normal);
}

.game-categories .category:hover h3 {
    color: var(--accent-gold);
}

/* ===== BONUS PROMOTIONS SECTION ===== */
.bonus-promotions {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(42, 42, 62, 0.3) 100%);
    border: 2px solid var(--accent-gold);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.bonus-promotions::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: rotate 10s linear infinite;
    z-index: -1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.bonus-promotions h2 {
    color: var(--accent-gold);
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.bonus-promotions p {
    font-size: 1.3rem;
    color: var(--text-light);
    font-weight: 500;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    background: linear-gradient(135deg, rgba(42, 42, 62, 0.4) 0%, rgba(26, 26, 46, 0.4) 100%);
}

.testimonials blockquote {
    background: var(--card-bg);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-gold);
    font-style: italic;
    position: relative;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.testimonials blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--accent-gold);
    font-family: var(--font-display);
    line-height: 1;
}

.testimonials blockquote:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--accent-gold-light);
}

/* ===== FORM STYLES ===== */
.cta-form {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(42, 42, 62, 0.9) 100%);
    border: 2px solid var(--accent-gold);
}

.cta-form form {
    display: grid;
    gap: var(--spacing-md);
    max-width: 500px;
    margin: 0 auto;
}

.cta-form label {
    color: var(--accent-gold);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.cta-form input {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: rgba(26, 26, 46, 0.8);
    color: var(--text-light);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.cta-form input:focus {
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
    background: rgba(26, 26, 46, 1);
}

.cta-form input::placeholder {
    color: var(--text-gray);
}

.cta-form button {
    margin-top: var(--spacing-md);
    justify-self: center;
}

#form-message {
    text-align: center;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    margin-top: var(--spacing-sm);
    font-weight: 500;
}

#form-message.success {
    background: rgba(0, 255, 136, 0.1);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

#form-message.error {
    background: rgba(255, 71, 87, 0.1);
    color: var(--danger-red);
    border: 1px solid var(--danger-red);
}

/* ===== FAQ SECTION ===== */
.faq {
    background: linear-gradient(135deg, rgba(42, 42, 62, 0.3) 0%, rgba(26, 26, 46, 0.3) 100%);
}

.faq-item {
    background: var(--card-bg);
    margin: var(--spacing-lg) 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.faq-item:hover {
    border-color: var(--accent-gold);
    box-shadow: var(--shadow-md);
}

.faq-item h3 {
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
    color: var(--text-dark);
    padding: var(--spacing-md) var(--spacing-lg);
    margin: 0;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.faq-item h3::after {
    content: '+';
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    font-weight: bold;
    transition: transform var(--transition-normal);
}

.faq-item:hover h3 {
    background: linear-gradient(135deg, var(--accent-gold-light) 0%, var(--accent-gold) 100%);
}

.faq-item:hover h3::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-item p {
    padding: var(--spacing-lg);
    margin: 0;
    color: var(--text-gray);
    line-height: 1.7;
}

/* ===== FOOTER ===== */
footer {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #0a0a1a 100%);
    padding: var(--spacing-xxl) var(--spacing-md) var(--spacing-lg);
    text-align: center;
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-xxl);
}

footer p {
    color: var(--text-gray);
    margin-bottom: var(--spacing-sm);
}

footer ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
}

footer ul li a {
    color: var(--text-gray);
    text-decoration: none;
    transition: color var(--transition-normal);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

footer ul li a:hover {
    color: var(--accent-gold);
    background: rgba(255, 215, 0, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --spacing-xl: 2rem;
        --spacing-xxl: 3rem;
    }

    .hero {
        min-height: 80vh;
        padding: var(--spacing-xl) var(--spacing-sm);
        /* Ensure proper spacing on mobile */
        gap: var(--spacing-md);
    }

    .hero h1 {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .hero p {
        font-size: 1.1rem;
    }

    section {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-lg) var(--spacing-sm);
    }

    .why-choose-us ul {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .game-categories .category {
        padding: var(--spacing-lg);
    }

    .cta-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 1rem;
    }

    footer ul {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero {
        padding: var(--spacing-lg) var(--spacing-xs);
        min-height: 70vh; /* Reduce height on very small screens */
        gap: var(--spacing-sm); /* Tighter spacing on small screens */
    }

    section {
        padding: var(--spacing-md) var(--spacing-xs);
        margin: var(--spacing-md) 0;
    }

    h2::after {
        width: 40px;
        height: 2px;
    }

    .testimonials blockquote {
        padding: var(--spacing-md);
    }

    .cta-form form {
        padding: 0 var(--spacing-xs);
    }
}

/* ===== ACCESSIBILITY & FOCUS STATES ===== */
*:focus {
    outline: 2px solid var(--accent-gold);
    outline-offset: 2px;
}

.cta-btn:focus {
    outline: 3px solid var(--accent-gold-light);
    outline-offset: 3px;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.hero {
    animation: fadeInUp 1s ease-out;
}

.cta-btn {
    animation: pulse 2s infinite;
}

.cta-btn:hover {
    animation: none;
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-gold {
    color: var(--accent-gold);
}

.bg-card {
    background: var(--card-bg);
}

.shadow-gold {
    box-shadow: var(--shadow-gold);
}

/* ===== PRINT STYLES ===== */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .cta-btn {
        border: 2px solid black;
        background: white !important;
    }
}
